<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双层iframe滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 2000px;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
        }
        
        .test-container {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        iframe {
            width: 100%;
            border: 2px solid #28a745;
            border-radius: 4px;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 8px;
            margin: 15px 0;
            display: inline-block;
            border-radius: 4px;
        }
        
        .scroll-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            min-width: 180px;
        }
    </style>
</head>
<body>
    <div class="scroll-info" id="scrollInfo">
        <div><strong>主页面滚动:</strong> <span id="mainScroll">0</span>px</div>
        <div style="margin-top: 5px; font-size: 10px; color: #ccc;">
            双层iframe嵌套测试
        </div>
    </div>
    
    <h1>双层iframe嵌套滚动测试</h1>
    <p>这个页面用于测试双层iframe嵌套中的划词菜单和便签位置计算。</p>
    
    <div class="test-container">
        <h2>主页面测试区域</h2>
        <div class="highlight">在主页面进行划词测试 - 这应该工作正常</div>
        <p>这是主页面的内容，用于对比测试。</p>
    </div>
    
    <div class="test-container">
        <h2>第一层iframe（高度400px）</h2>
        <iframe height="400" srcdoc='
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>第一层iframe</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 1500px;
            background: linear-gradient(to bottom, #fff3e0, #ffe0b2);
        }
        .container {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border: 2px solid #ff9800;
            border-radius: 6px;
        }
        iframe {
            width: 100%;
            border: 2px solid #f44336;
            border-radius: 4px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 6px;
            margin: 10px 0;
            display: inline-block;
            border-radius: 3px;
        }
        .scroll-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255,152,0,0.9);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="level1ScrollInfo">
        第1层滚动: <span id="level1Scroll">0</span>px
    </div>
    
    <h2>第一层iframe内容</h2>
    <p>这是第一层iframe，请向下滚动查看第二层iframe。</p>
    
    <div class="container">
        <div class="highlight">第一层iframe测试文本 - 可以在这里划词</div>
        <p>在这一层进行划词测试，验证位置计算。</p>
    </div>
    
    <div class="container">
        <h3>第二层iframe（高度300px）- 关键测试区域</h3>
        <iframe height="300" srcdoc="<!DOCTYPE html>
<html>
<head>
    <meta charset=&quot;UTF-8&quot;>
    <title>第二层iframe</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            height: 1000px;
            background: linear-gradient(to bottom, #e8f5e8, #c8e6c9);
        }
        .test-area {
            margin: 15px 0;
            padding: 12px;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 5px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 5px;
            margin: 8px 0;
            display: inline-block;
            border-radius: 3px;
            font-weight: bold;
        }
        .scroll-indicator {
            position: fixed;
            top: 50px;
            left: 10px;
            background: rgba(76,175,80,0.9);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
        }
        .critical-test {
            background: #fff3e0;
            border: 3px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class=&quot;scroll-indicator&quot; id=&quot;level2ScrollInfo&quot;&gt;
        第2层滚动: &lt;span id=&quot;level2Scroll&quot;&gt;0&lt;/span&gt;px
    &lt;/div&gt;
    
    &lt;h3&gt;第二层iframe内容（最深层）&lt;/h3&gt;
    &lt;p&gt;这是最深层的iframe，在这里进行关键测试。&lt;/p&gt;
    
    &lt;div class=&quot;critical-test&quot;&gt;
        &lt;h4&gt;🎯 关键测试区域&lt;/h4&gt;
        &lt;div class=&quot;highlight&quot;&gt;请选择这段文字进行划词测试&lt;/div&gt;
        &lt;p&gt;在不同滚动位置下测试划词菜单位置是否正确。&lt;/p&gt;
        &lt;div class=&quot;highlight&quot;&gt;右键点击这里创建便签测试&lt;/div&gt;
        &lt;p&gt;验证便签创建位置是否准确。&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class=&quot;test-area&quot;&gt;
        &lt;p&gt;继续向下滚动，在不同位置进行测试...&lt;/p&gt;
        &lt;div class=&quot;highlight&quot;&gt;滚动后的测试文本1&lt;/div&gt;
        &lt;p&gt;测试滚动后的位置计算准确性。&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class=&quot;test-area&quot;&gt;
        &lt;div class=&quot;highlight&quot;&gt;滚动后的测试文本2&lt;/div&gt;
        &lt;p&gt;在更深的滚动位置进行测试。&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class=&quot;test-area&quot;&gt;
        &lt;div class=&quot;highlight&quot;&gt;最后的测试文本&lt;/div&gt;
        &lt;p&gt;在最底部进行最终测试。&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;script&gt;
        function updateLevel2ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById(&quot;level2Scroll&quot;);
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener(&quot;scroll&quot;, updateLevel2ScrollInfo);
        updateLevel2ScrollInfo();
        
        console.log(&quot;第二层iframe已加载&quot;);
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;"></iframe>
    </div>
    
    <div class="container">
        <div class="highlight">第一层iframe底部测试文本</div>
        <p>在第一层iframe的底部进行测试。</p>
    </div>
    
    <script>
        function updateLevel1ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById("level1Scroll");
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener("scroll", updateLevel1ScrollInfo);
        updateLevel1ScrollInfo();
        
        console.log("第一层iframe已加载");
    </script>
</body>
</html>
        '></iframe>
    </div>
    
    <div class="test-container">
        <h2>更多主页面内容</h2>
        <p>继续向下滚动，测试不同滚动位置下的功能。</p>
        <div class="highlight">在不同位置测试划词功能</div>
    </div>
    
    <script>
        function updateMainScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            document.getElementById('mainScroll').textContent = Math.round(scrollPos);
        }
        
        window.addEventListener('scroll', updateMainScrollInfo);
        updateMainScrollInfo();
        
        console.log('双层iframe测试页面已加载');
        console.log('请在第二层iframe中进行划词和右键测试');
    </script>
</body>
</html>
