<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单iframe滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 200vh;
        }
        
        .container {
            border: 2px solid #333;
            margin: 20px 0;
            padding: 10px;
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #666;
            margin: 10px 0;
        }
        
        .test-text {
            background: #ffffcc;
            padding: 10px;
            margin: 10px 0;
            border: 1px dashed #999;
        }
    </style>
</head>
<body>
    <h1>简单iframe滚动测试</h1>
    
    <div class="container">
        <h2>顶层页面内容</h2>
        <div class="test-text">
            这是顶层页面的可选择文本。请尝试选择这段文字来测试划词功能。
        </div>
    </div>
    
    <div class="container">
        <h2>iframe内容</h2>
        <iframe srcdoc='
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { 
                    margin: 0; 
                    padding: 20px; 
                    font-family: Arial, sans-serif;
                    height: 150vh;
                }
                .scrollable {
                    height: 200px;
                    overflow: auto;
                    border: 1px solid #ccc;
                    padding: 10px;
                    margin: 10px 0;
                }
                .content {
                    height: 400px;
                    background: linear-gradient(to bottom, #e0f0ff, #d0e0ff);
                    padding: 20px;
                }
                .test-text {
                    background: #ccffcc;
                    padding: 10px;
                    margin: 10px 0;
                    border: 1px dashed #999;
                }
            </style>
        </head>
        <body>
            <h3>iframe内容</h3>
            <div class="test-text">
                这是iframe中的可选择文本。请选择这段文字测试划词功能。
            </div>
            
            <div class="scrollable">
                <div class="content">
                    <p>iframe的滚动区域</p>
                    <div class="test-text">
                        在iframe的滚动区域内选择这段文字，测试滚动偏移的处理。
                    </div>
                    <p>更多内容...</p>
                    <p>继续滚动...</p>
                    <p>测试滚动偏移...</p>
                </div>
            </div>
        </body>
        </html>'></iframe>
    </div>
    
    <div class="container">
        <h2>测试说明</h2>
        <ul>
            <li>请在iframe中选择文字</li>
            <li>测试在有滚动条的区域内选择文字</li>
            <li>观察划词工具栏的位置是否正确</li>
            <li>右键点击测试便签创建位置</li>
        </ul>
    </div>
</body>
</html>
