<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复杂多层iframe嵌套滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 3000px;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
        }
        
        .test-container {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 3px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        iframe {
            width: 100%;
            border: 2px solid #28a745;
            border-radius: 4px;
        }
        
        .scroll-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 8px;
            margin: 15px 0;
            display: inline-block;
            border-radius: 4px;
        }
        
        .test-instruction {
            background: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="scroll-info" id="scrollInfo">
        <div><strong>主页面滚动:</strong> <span id="mainScroll">0</span>px</div>
        <div style="margin-top: 8px; font-size: 10px; color: #ccc;">
            测试多层iframe嵌套滚动
        </div>
    </div>
    
    <h1>复杂多层iframe嵌套滚动测试</h1>
    
    <div class="test-instruction">
        <h3>测试说明</h3>
        <ol>
            <li>这个页面包含4层iframe嵌套</li>
            <li>每一层都有独立的滚动条</li>
            <li>在最深层iframe中进行划词和右键操作</li>
            <li>验证划词菜单和便签位置是否准确</li>
            <li>打开浏览器开发者工具查看详细的调试信息</li>
        </ol>
    </div>
    
    <div class="test-container">
        <h2>主页面测试区域</h2>
        <div class="highlight">在主页面进行划词测试 - 这应该工作正常</div>
        <p>这是主页面的内容，用于对比测试。</p>
    </div>
    
    <div class="test-container">
        <h2>第一层iframe（高度500px）</h2>
        <iframe height="500" srcdoc='
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>第一层iframe</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 2000px;
            background: linear-gradient(to bottom, #fff3e0, #ffe0b2);
        }
        .container {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border: 2px solid #ff9800;
            border-radius: 6px;
        }
        iframe {
            width: 100%;
            border: 2px solid #f44336;
            border-radius: 4px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 6px;
            margin: 10px 0;
            display: inline-block;
            border-radius: 3px;
        }
        .scroll-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255,152,0,0.9);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="level1ScrollInfo">
        第1层滚动: <span id="level1Scroll">0</span>px
    </div>
    
    <h2>第一层iframe内容</h2>
    <p>这是第一层iframe，请向下滚动查看第二层iframe。</p>
    
    <div class="container">
        <div class="highlight">第一层iframe测试文本 - 可以在这里划词</div>
        <p>在这一层进行划词测试，验证位置计算。</p>
    </div>
    
    <div class="container">
        <h3>第二层iframe（高度400px）</h3>
        <iframe height="400" srcdoc="<!DOCTYPE html>
<html>
<head>
    <meta charset=&quot;UTF-8&quot;>
    <title>第二层iframe</title>
    <style>
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            height: 1800px;
            background: linear-gradient(to bottom, #e8f5e8, #c8e6c9);
        }
        .container {
            margin: 15px 0;
            padding: 12px;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 5px;
        }
        iframe {
            width: 100%;
            border: 2px solid #9c27b0;
            border-radius: 4px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 5px;
            margin: 8px 0;
            display: inline-block;
            border-radius: 3px;
        }
        .scroll-indicator {
            position: fixed;
            top: 50px;
            left: 10px;
            background: rgba(76,175,80,0.9);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 10px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class=&quot;scroll-indicator&quot; id=&quot;level2ScrollInfo&quot;>
        第2层滚动: <span id=&quot;level2Scroll&quot;>0</span>px
    </div>
    
    <h3>第二层iframe内容</h3>
    <p>这是第二层iframe，继续向下滚动查看第三层iframe。</p>
    
    <div class=&quot;container&quot;>
        <div class=&quot;highlight&quot;>第二层iframe测试文本 - 可以在这里划词</div>
        <p>在这一层进行划词测试，验证多层嵌套的位置计算。</p>
    </div>
    
    <div class=&quot;container&quot;>
        <h4>第三层iframe（高度300px）</h4>
        <iframe height=&quot;300&quot; srcdoc=&quot;&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;meta charset=\\&quot;UTF-8\\&quot;&gt;
    &lt;title&gt;第三层iframe&lt;/title&gt;
    &lt;style&gt;
        body {
            margin: 0;
            padding: 12px;
            font-family: Arial, sans-serif;
            height: 1500px;
            background: linear-gradient(to bottom, #f3e5f5, #e1bee7);
        }
        .container {
            margin: 12px 0;
            padding: 10px;
            background: white;
            border: 2px solid #9c27b0;
            border-radius: 4px;
        }
        iframe {
            width: 100%;
            border: 2px solid #e91e63;
            border-radius: 3px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 4px;
            margin: 6px 0;
            display: inline-block;
            border-radius: 2px;
        }
        .scroll-indicator {
            position: fixed;
            top: 90px;
            left: 10px;
            background: rgba(156,39,176,0.9);
            color: white;
            padding: 6px;
            border-radius: 3px;
            font-size: 9px;
            z-index: 1000;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class=\\&quot;scroll-indicator\\&quot; id=\\&quot;level3ScrollInfo\\&quot;&gt;
        第3层滚动: &lt;span id=\\&quot;level3Scroll\\&quot;&gt;0&lt;/span&gt;px
    &lt;/div&gt;
    
    &lt;h4&gt;第三层iframe内容&lt;/h4&gt;
    &lt;p&gt;这是第三层iframe，继续向下滚动查看第四层iframe。&lt;/p&gt;
    
    &lt;div class=\\&quot;container\\&quot;&gt;
        &lt;div class=\\&quot;highlight\\&quot;&gt;第三层iframe测试文本 - 可以在这里划词&lt;/div&gt;
        &lt;p&gt;在这一层进行划词测试，验证三层嵌套的位置计算。&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;div class=\\&quot;container\\&quot;&gt;
        &lt;h5&gt;第四层iframe（高度250px）- 最深层测试&lt;/h5&gt;
        &lt;iframe height=\\&quot;250\\&quot; srcdoc=\\&quot;&amp;lt;!DOCTYPE html&amp;gt;
&amp;lt;html&amp;gt;
&amp;lt;head&amp;gt;
    &amp;lt;meta charset=\\\\&amp;quot;UTF-8\\\\&amp;quot;&amp;gt;
    &amp;lt;title&amp;gt;第四层iframe&amp;lt;/title&amp;gt;
    &amp;lt;style&amp;gt;
        body {
            margin: 0;
            padding: 10px;
            font-family: Arial, sans-serif;
            height: 1200px;
            background: linear-gradient(to bottom, #ffebee, #ffcdd2);
        }
        .test-area {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border: 2px solid #e91e63;
            border-radius: 3px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 3px;
            margin: 4px 0;
            display: inline-block;
            border-radius: 2px;
            font-weight: bold;
        }
        .scroll-indicator {
            position: fixed;
            top: 130px;
            left: 10px;
            background: rgba(233,30,99,0.9);
            color: white;
            padding: 5px;
            border-radius: 3px;
            font-size: 8px;
            z-index: 1000;
        }
        .critical-test {
            background: #fff3e0;
            border: 3px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
    &amp;lt;/style&amp;gt;
&amp;lt;/head&amp;gt;
&amp;lt;body&amp;gt;
    &amp;lt;div class=\\\\&amp;quot;scroll-indicator\\\\&amp;quot; id=\\\\&amp;quot;level4ScrollInfo\\\\&amp;quot;&amp;gt;
        第4层滚动: &amp;lt;span id=\\\\&amp;quot;level4Scroll\\\\&amp;quot;&amp;gt;0&amp;lt;/span&amp;gt;px
    &amp;lt;/div&amp;gt;
    
    &amp;lt;h5&amp;gt;第四层iframe内容（最深层）&amp;lt;/h5&amp;gt;
    &amp;lt;p&amp;gt;这是最深层的iframe，在这里进行关键测试。&amp;lt;/p&amp;gt;
    
    &amp;lt;div class=\\\\&amp;quot;critical-test\\\\&amp;quot;&amp;gt;
        &amp;lt;h6&amp;gt;🎯 关键测试区域&amp;lt;/h6&amp;gt;
        &amp;lt;div class=\\\\&amp;quot;highlight\\\\&amp;quot;&amp;gt;请选择这段文字进行划词测试&amp;lt;/div&amp;gt;
        &amp;lt;p&amp;gt;在不同滚动位置下测试划词菜单位置是否正确。&amp;lt;/p&amp;gt;
        &amp;lt;div class=\\\\&amp;quot;highlight\\\\&amp;quot;&amp;gt;右键点击这里创建便签测试&amp;lt;/div&amp;gt;
        &amp;lt;p&amp;gt;验证便签创建位置是否准确。&amp;lt;/p&amp;gt;
    &amp;lt;/div&amp;gt;
    
    &amp;lt;div class=\\\\&amp;quot;test-area\\\\&amp;quot;&amp;gt;
        &amp;lt;p&amp;gt;继续向下滚动，在不同位置进行测试...&amp;lt;/p&amp;gt;
        &amp;lt;div class=\\\\&amp;quot;highlight\\\\&amp;quot;&amp;gt;滚动后的测试文本1&amp;lt;/div&amp;gt;
        &amp;lt;p&amp;gt;测试滚动后的位置计算准确性。&amp;lt;/p&amp;gt;
    &amp;lt;/div&amp;gt;
    
    &amp;lt;div class=\\\\&amp;quot;test-area\\\\&amp;quot;&amp;gt;
        &amp;lt;div class=\\\\&amp;quot;highlight\\\\&amp;quot;&amp;gt;滚动后的测试文本2&amp;lt;/div&amp;gt;
        &amp;lt;p&amp;gt;在更深的滚动位置进行测试。&amp;lt;/p&amp;gt;
    &amp;lt;/div&amp;gt;
    
    &amp;lt;div class=\\\\&amp;quot;test-area\\\\&amp;quot;&amp;gt;
        &amp;lt;div class=\\\\&amp;quot;highlight\\\\&amp;quot;&amp;gt;最后的测试文本&amp;lt;/div&amp;gt;
        &amp;lt;p&amp;gt;在最底部进行最终测试。&amp;lt;/p&amp;gt;
    &amp;lt;/div&amp;gt;
    
    &amp;lt;script&amp;gt;
        function updateLevel4ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById(\\\\&amp;quot;level4Scroll\\\\&amp;quot;);
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener(\\\\&amp;quot;scroll\\\\&amp;quot;, updateLevel4ScrollInfo);
        updateLevel4ScrollInfo();
        
        console.log(\\\\&amp;quot;第四层iframe已加载\\\\&amp;quot;);
    &amp;lt;/script&amp;gt;
&amp;lt;/body&amp;gt;
&amp;lt;/html&amp;gt;\\&quot;&amp;gt;&amp;lt;/iframe&amp;gt;
    &amp;lt;/div&amp;gt;
    
    &amp;lt;script&amp;gt;
        function updateLevel3ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById(\\&quot;level3Scroll\\&quot;);
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener(\\&quot;scroll\\&quot;, updateLevel3ScrollInfo);
        updateLevel3ScrollInfo();
        
        console.log(\\&quot;第三层iframe已加载\\&quot;);
    &amp;lt;/script&amp;gt;
&amp;lt;/body&amp;gt;
&amp;lt;/html&amp;gt;&quot;&gt;&lt;/iframe&gt;
    &lt;/div&gt;
    
    &lt;script&gt;
        function updateLevel2ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById(&quot;level2Scroll&quot;);
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener(&quot;scroll&quot;, updateLevel2ScrollInfo);
        updateLevel2ScrollInfo();
        
        console.log(&quot;第二层iframe已加载&quot;);
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;"></iframe>
    </div>
    
    <script>
        function updateLevel1ScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById("level1Scroll");
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener("scroll", updateLevel1ScrollInfo);
        updateLevel1ScrollInfo();
        
        console.log("第一层iframe已加载");
    </script>
</body>
</html>
        '></iframe>
    </div>
    
    <div class="test-container">
        <h2>更多主页面内容</h2>
        <p>继续向下滚动，测试不同滚动位置下的功能。</p>
        <div class="highlight">在不同位置测试划词功能</div>
    </div>
    
    <script>
        function updateMainScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            document.getElementById('mainScroll').textContent = Math.round(scrollPos);
        }
        
        window.addEventListener('scroll', updateMainScrollInfo);
        updateMainScrollInfo();
        
        console.log('复杂多层iframe测试页面已加载');
        console.log('请在最深层iframe中进行划词和右键测试');
    </script>
</body>
</html>
