<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单iframe滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 2000px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
        }
        
        .test-area {
            margin: 50px 0;
            padding: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #28a745;
            border-radius: 4px;
        }
        
        .highlight {
            background-color: yellow;
            padding: 5px;
            margin: 10px 0;
            display: inline-block;
        }
        
        .scroll-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-info" id="scrollInfo">
        主页面滚动: <span id="mainScroll">0</span>px
    </div>
    
    <h1>简单iframe滚动测试</h1>
    <p>这个页面用于测试iframe中的划词菜单和便签位置计算。</p>
    
    <div class="test-area">
        <h2>测试步骤</h2>
        <ol>
            <li>滚动主页面到不同位置</li>
            <li>在iframe内滚动到不同位置</li>
            <li>在iframe内选择文本进行划词</li>
            <li>右键点击创建便签</li>
            <li>验证划词菜单和便签位置是否正确</li>
        </ol>
    </div>
    
    <div class="test-area">
        <h2>主页面测试区域</h2>
        <div class="highlight">在主页面进行划词测试</div>
        <p>这是主页面的内容，可以在这里测试划词功能。</p>
    </div>
    
    <div class="test-area">
        <h2>iframe测试区域</h2>
        <iframe srcdoc='
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>iframe内容</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 1500px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
        }
        .content-block {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 5px;
            margin: 10px 0;
            display: inline-block;
        }
        .scroll-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255,0,0,0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="iframeScrollInfo">
        iframe滚动: <span id="iframeScroll">0</span>px
    </div>
    
    <h2>iframe内容区域</h2>
    <p>这是iframe内的内容，请向下滚动查看更多内容。</p>
    
    <div class="content-block">
        <h3>测试区域1</h3>
        <div class="highlight">请在这里进行划词测试</div>
        <p>选择这段文字进行划词测试，验证划词菜单位置是否正确。</p>
    </div>
    
    <div class="content-block">
        <h3>测试区域2</h3>
        <p>这里有更多的测试内容。滚动到这里后，右键点击创建便签。</p>
        <div class="highlight">测试便签创建位置是否正确</div>
    </div>
    
    <div class="content-block">
        <h3>测试区域3</h3>
        <p>继续向下滚动，测试不同滚动位置下的功能。</p>
        <div class="highlight">在不同滚动位置测试划词和便签功能</div>
    </div>
    
    <div class="content-block">
        <h3>测试区域4</h3>
        <p>这是最后一个测试区域。</p>
        <div class="highlight">最后的测试文本</div>
        <p>测试完成后，检查所有功能是否正常工作。</p>
    </div>
    
    <script>
        function updateIframeScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById("iframeScroll");
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener("scroll", updateIframeScrollInfo);
        updateIframeScrollInfo();
        
        console.log("iframe内容已加载，滚动位置:", window.pageYOffset);
    </script>
</body>
</html>
        '></iframe>
    </div>
    
    <div class="test-area">
        <h2>更多主页面内容</h2>
        <p>继续向下滚动，测试不同滚动位置下的功能。</p>
        <div class="highlight">在不同位置测试划词功能</div>
    </div>
    
    <script>
        function updateMainScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            document.getElementById('mainScroll').textContent = Math.round(scrollPos);
        }
        
        window.addEventListener('scroll', updateMainScrollInfo);
        updateMainScrollInfo();
        
        console.log('主页面已加载，开始测试iframe滚动场景');
    </script>
</body>
</html>
