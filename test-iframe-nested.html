<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层iframe嵌套滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 200vh; /* 让页面可以滚动 */
        }
        
        .container {
            border: 2px solid #333;
            margin: 20px 0;
            padding: 10px;
        }
        
        .scrollable {
            height: 300px;
            overflow: auto;
            border: 1px solid #ccc;
            padding: 10px;
        }
        
        .content {
            height: 600px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            padding: 20px;
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 2px solid #666;
            margin: 10px 0;
        }
        
        .test-text {
            background: #ffffcc;
            padding: 10px;
            margin: 10px 0;
            border: 1px dashed #999;
        }
    </style>
</head>
<body>
    <h1>多层iframe嵌套滚动测试页面</h1>
    
    <div class="container">
        <h2>顶层页面内容</h2>
        <div class="test-text">
            这是顶层页面的可选择文本。请尝试选择这段文字来测试划词功能。
        </div>
        
        <div class="scrollable">
            <div class="content">
                <p>这是顶层页面的滚动区域内容。</p>
                <div class="test-text">
                    在滚动区域内选择这段文字来测试划词功能的位置是否正确。
                </div>
                <p>更多内容...</p>
                <p>继续滚动...</p>
                <p>测试滚动偏移...</p>
            </div>
        </div>
    </div>
    
    <!-- 第一层iframe -->
    <div class="container">
        <h2>第一层iframe</h2>
        <iframe src="data:text/html;charset=utf-8,
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { 
                    margin: 0; 
                    padding: 20px; 
                    font-family: Arial, sans-serif;
                    height: 150vh;
                }
                .scrollable {
                    height: 200px;
                    overflow: auto;
                    border: 1px solid #ccc;
                    padding: 10px;
                    margin: 10px 0;
                }
                .content {
                    height: 400px;
                    background: linear-gradient(to bottom, #e0f0ff, #d0e0ff);
                    padding: 20px;
                }
                .test-text {
                    background: #ccffcc;
                    padding: 10px;
                    margin: 10px 0;
                    border: 1px dashed #999;
                }
                iframe {
                    width: 100%;
                    height: 300px;
                    border: 2px solid #999;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <h3>第一层iframe内容</h3>
            <div class='test-text'>
                这是第一层iframe中的可选择文本。请选择这段文字测试划词功能。
            </div>
            
            <div class='scrollable'>
                <div class='content'>
                    <p>第一层iframe的滚动区域</p>
                    <div class='test-text'>
                        在第一层iframe的滚动区域内选择这段文字。
                    </div>
                    <p>更多内容...</p>
                    <p>继续滚动...</p>
                </div>
            </div>
            
            <!-- 第二层iframe -->
            <iframe src='data:text/html;charset=utf-8,
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: Arial, sans-serif;
                        height: 120vh;
                    }
                    .scrollable {
                        height: 150px;
                        overflow: auto;
                        border: 1px solid #ccc;
                        padding: 10px;
                        margin: 10px 0;
                    }
                    .content {
                        height: 300px;
                        background: linear-gradient(to bottom, #ffe0e0, #ffd0d0);
                        padding: 20px;
                    }
                    .test-text {
                        background: #ffccff;
                        padding: 10px;
                        margin: 10px 0;
                        border: 1px dashed #999;
                    }
                </style>
            </head>
            <body>
                <h4>第二层iframe内容</h4>
                <div class=\"test-text\">
                    这是第二层iframe中的可选择文本。请选择这段文字测试多层嵌套的划词功能。
                </div>
                
                <div class=\"scrollable\">
                    <div class=\"content\">
                        <p>第二层iframe的滚动区域</p>
                        <div class=\"test-text\">
                            在第二层iframe的滚动区域内选择这段文字，测试多层嵌套滚动的划词位置是否正确。
                        </div>
                        <p>更多内容...</p>
                        <p>继续滚动...</p>
                        <p>测试深层嵌套...</p>
                    </div>
                </div>
            </body>
            </html>'></iframe>
        </body>
        </html>"></iframe>
    </div>
    
    <div class="container">
        <h2>测试说明</h2>
        <ul>
            <li>请在不同层级的iframe中选择文字</li>
            <li>测试在有滚动条的区域内选择文字</li>
            <li>观察划词工具栏和便签创建的位置是否正确</li>
            <li>特别注意多层嵌套iframe中的坐标计算</li>
        </ul>
    </div>
</body>
</html>
