<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础iframe功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 1500px;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
        }
        
        .test-container {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        iframe {
            width: 100%;
            height: 300px;
            border: 2px solid #28a745;
            border-radius: 4px;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 8px;
            margin: 15px 0;
            display: inline-block;
            border-radius: 4px;
        }
        
        .scroll-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-info" id="scrollInfo">
        主页面滚动: <span id="mainScroll">0</span>px
    </div>
    
    <h1>基础iframe功能测试</h1>
    <p>这个页面用于测试基本的iframe划词和便签功能是否正常工作。</p>
    
    <div class="test-container">
        <h2>主页面测试区域</h2>
        <div class="highlight">在主页面进行划词测试 - 这应该工作正常</div>
        <p>这是主页面的内容，用于对比测试。右键点击可以创建便签。</p>
    </div>
    
    <div class="test-container">
        <h2>单层iframe测试</h2>
        <p>下面是一个简单的iframe，测试基本的划词和便签功能：</p>
        <iframe srcdoc='
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>iframe内容</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            height: 800px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
        }
        .content-block {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 5px;
            margin: 10px 0;
            display: inline-block;
            border-radius: 3px;
        }
        .scroll-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(33,150,243,0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="iframeScrollInfo">
        iframe滚动: <span id="iframeScroll">0</span>px
    </div>
    
    <h2>iframe内容区域</h2>
    <p>这是iframe内的内容，请测试划词和便签功能。</p>
    
    <div class="content-block">
        <h3>测试区域1</h3>
        <div class="highlight">请选择这段文字进行划词测试</div>
        <p>选择这段文字应该能正常显示划词菜单。</p>
    </div>
    
    <div class="content-block">
        <h3>测试区域2</h3>
        <p>这里有更多的测试内容。</p>
        <div class="highlight">右键点击这里创建便签</div>
        <p>右键点击应该能在正确位置创建便签。</p>
    </div>
    
    <div class="content-block">
        <h3>滚动测试区域</h3>
        <p>向下滚动iframe，然后在不同位置测试功能。</p>
        <div class="highlight">滚动后的划词测试文本</div>
        <p>在滚动后的位置测试划词菜单位置是否正确。</p>
    </div>
    
    <div class="content-block">
        <h3>最后的测试区域</h3>
        <div class="highlight">最后的测试文本</div>
        <p>在iframe底部进行最终测试。</p>
    </div>
    
    <script>
        function updateIframeScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            const scrollElement = document.getElementById("iframeScroll");
            if (scrollElement) {
                scrollElement.textContent = Math.round(scrollPos);
            }
        }
        
        window.addEventListener("scroll", updateIframeScrollInfo);
        updateIframeScrollInfo();
        
        console.log("iframe内容已加载");
    </script>
</body>
</html>
        '></iframe>
    </div>
    
    <div class="test-container">
        <h2>更多主页面内容</h2>
        <p>继续向下滚动，测试不同滚动位置下的功能。</p>
        <div class="highlight">在不同位置测试划词功能</div>
        <p>这里可以测试主页面在不同滚动位置下的功能。</p>
    </div>
    
    <script>
        function updateMainScrollInfo() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            document.getElementById('mainScroll').textContent = Math.round(scrollPos);
        }
        
        window.addEventListener('scroll', updateMainScrollInfo);
        updateMainScrollInfo();
        
        console.log('基础iframe测试页面已加载');
        console.log('请测试iframe中的划词和右键功能');
    </script>
</body>
</html>
