import { defineContentScript } from "wxt/sandbox";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_idle",
  allFrames: true, // 让它能注入到 iframe
  main(ctx) {
    if (window.top !== window) {
      // document.addEventListener("mouseup", () => {
      //   console.log("22333333")
      //   const selection = window.getSelection();
      //   if (!selection || selection.isCollapsed) return;

      //   const range = selection.getRangeAt(0);
      //   const rect = range.getBoundingClientRect();

      //   window.top?.postMessage(
      //     {
      //       type: "iframe-selection",
      //       text: selection.toString(),
      //       rect: {
      //         top: rect.top,
      //         left: rect.left,
      //         width: rect.width,
      //         height: rect.height,
      //       },
      //     },
      //     "*",
      //   );
      // });
      // document.addEventListener("mouseup", (e) => {
      //   const target = e.target;
      //   if (target && (target.tagName === "INPUT" || target.tagName === "TEXTAREA")) {
      //     const el = target
      //     const start = el.selectionStart ?? 0;
      //     const end = el.selectionEnd ?? 0;
      //     const text = el.value.substring(start, end);
      //     console.log(text, 1231)
      //     if (text) {
      //       const rect = el.getBoundingClientRect();
      //       window.top?.postMessage(
      //         {
      //           type: "iframe-input-selection",
      //           text,
      //           rect: {
      //             top: rect.top,
      //             left: rect.left,
      //             width: rect.width,
      //             height: rect.height,
      //           },
      //         },
      //         "*",
      //       );
      //     }
      //   }
      // });

      // 计算当前iframe在整个嵌套结构中的路径和累积偏移
      function getIframePathAndOffset() {
        const path = [];
        let currentWindow = window;
        let cumulativeOffset = { top: 0, left: 0 };

        // 记录当前iframe内部的滚动偏移（但不直接加到累积偏移中）
        const currentScrollTop = currentWindow.pageYOffset || currentWindow.document.documentElement.scrollTop || currentWindow.document.body.scrollTop || 0;
        const currentScrollLeft = currentWindow.pageXOffset || currentWindow.document.documentElement.scrollLeft || currentWindow.document.body.scrollLeft || 0;

        // 从当前窗口向上遍历到顶层
        let level = 1;
        while (currentWindow !== window.top && currentWindow.parent !== currentWindow) {
          try {
            // 在父窗口中找到当前窗口对应的iframe元素
            const parentDoc = currentWindow.parent.document;
            const iframes = parentDoc.querySelectorAll('iframe');

            for (let i = 0; i < iframes.length; i++) {
              if (iframes[i].contentWindow === currentWindow) {
                const rect = iframes[i].getBoundingClientRect();

                // 获取父窗口的滚动偏移
                const parentScrollTop = currentWindow.parent.pageYOffset || parentDoc.documentElement.scrollTop || parentDoc.body.scrollTop || 0;
                const parentScrollLeft = currentWindow.parent.pageXOffset || parentDoc.documentElement.scrollLeft || parentDoc.body.scrollLeft || 0;

                // 累积偏移：iframe在父窗口中的视口位置 + 父窗口的滚动偏移
                // rect.top/left 是相对于父窗口视口的坐标
                // 需要加上父窗口的滚动偏移来得到相对于父窗口文档的绝对位置
                cumulativeOffset.top += rect.top + parentScrollTop;
                cumulativeOffset.left += rect.left + parentScrollLeft;

                // 记录这一层的滚动偏移信息
                scrollOffsets.push({
                  level: level,
                  scrollTop: parentScrollTop,
                  scrollLeft: parentScrollLeft,
                  iframeRect: {
                    top: rect.top,
                    left: rect.left,
                    width: rect.width,
                    height: rect.height
                  },
                  windowType: 'parent'
                });

                path.unshift(i); // 添加到路径开头
                break;
              }
            }
          } catch (e) {
            // 跨域访问限制，无法获取精确偏移
            console.debug('跨域iframe检测到，无法获取精确偏移，将使用消息传递方式');
            // 遇到跨域限制时，返回当前已计算的偏移
            return {
              path: [],
              offset: cumulativeOffset,
              currentScroll: { top: currentScrollTop, left: currentScrollLeft },
              allScrollOffsets: scrollOffsets
            };
          }

          currentWindow = currentWindow.parent;
          level++;
        }

        return {
          path,
          offset: cumulativeOffset,
          currentScroll: { top: currentScrollTop, left: currentScrollLeft }
        };
      }

      // 监听来自子iframe的消息并转发
      window.addEventListener("message", (e) => {
        // 只处理来自子iframe的消息
        if (e.source && e.source !== window && (e.data.type === "iframe-selection" || e.data.type === "iframe-mousedown" || e.data.type === "iframe-contextmenu")) {
          // 如果消息包含坐标信息，需要加上当前iframe的偏移
          // 对于有累积偏移的消息，需要更新累积偏移；对于没有累积偏移的消息，需要创建累积偏移
          if (e.data.type === "iframe-selection" && e.data.rect) {
            try {
              // 找到发送消息的子iframe
              const childIframes = document.querySelectorAll("iframe");
              for (const iframe of childIframes) {
                if (iframe.contentWindow === e.source) {
                  const iframeRect = iframe.getBoundingClientRect();
                  const scrollTop =
                    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
                  const scrollLeft =
                    window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

                  // 如果消息已经有累积偏移，说明来自更深层的iframe，需要累加当前层的偏移
                  if (e.data.cumulativeOffset) {
                    e.data.cumulativeOffset.top += iframeRect.top + scrollTop;
                    e.data.cumulativeOffset.left += iframeRect.left + scrollLeft;
                  } else {
                    // 如果没有累积偏移，创建初始累积偏移
                    e.data.cumulativeOffset = {
                      top: iframeRect.top + scrollTop,
                      left: iframeRect.left + scrollLeft,
                    };
                  }

                  // 累积每一层iframe的滚动偏移信息
                  if (!e.data.allIframeScrollOffsets) {
                    e.data.allIframeScrollOffsets = [];
                  }
                  // 将当前层的滚动偏移添加到数组中
                  e.data.allIframeScrollOffsets.push({
                    level: e.data.allIframeScrollOffsets.length,
                    scrollTop: scrollTop,
                    scrollLeft: scrollLeft,
                    iframeRect: {
                      top: iframeRect.top,
                      left: iframeRect.left,
                      width: iframeRect.width,
                      height: iframeRect.height
                    }
                  });

                  break;
                }
              }
            } catch (error) {
              console.warn("计算iframe偏移时出错:", error);
            }
          } else if (e.data.type === "iframe-contextmenu") {
            try {
              // 找到发送消息的子iframe
              const childIframes = document.querySelectorAll("iframe");
              for (const iframe of childIframes) {
                if (iframe.contentWindow === e.source) {
                  const iframeRect = iframe.getBoundingClientRect();
                  const scrollTop =
                    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
                  const scrollLeft =
                    window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

                  // 如果消息已经有累积偏移，说明来自更深层的iframe，需要累加当前层的偏移
                  if (e.data.cumulativeOffset) {
                    e.data.cumulativeOffset.top += iframeRect.top + scrollTop;
                    e.data.cumulativeOffset.left += iframeRect.left + scrollLeft;
                  } else {
                    // 如果没有累积偏移，创建初始累积偏移
                    e.data.cumulativeOffset = {
                      top: iframeRect.top + scrollTop,
                      left: iframeRect.left + scrollLeft,
                    };
                  }

                  // 重要：为多层iframe嵌套，需要将当前层的滚动偏移也加到累积偏移中
                  // 这样每一层的滚动都会被正确累积
                  if (e.data.needsCurrentLayerScroll) {
                    e.data.cumulativeOffset.top += scrollTop;
                    e.data.cumulativeOffset.left += scrollLeft;
                  }

                  break;
                }
              }
            } catch (error) {
              console.warn("计算iframe偏移时出错:", error);
            }
          }

          // 继续向上传递消息
          if (window.parent !== window) {
            window.parent.postMessage(e.data, "*");
          }
        }
      });

      document.addEventListener("pointerup", () => {
        setTimeout(() => {
          const selection = window.getSelection();
          if (!selection || selection.isCollapsed) return;

          const text = selection.toString().trim();
          if (!text) return;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          // 获取当前iframe的路径和累积偏移
          const { path, offset, currentScroll } = getIframePathAndOffset();

          // 获取当前iframe内部的滚动偏移
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

          const messageData = {
            type: "iframe-selection",
            text,
            rect: {
              // 保持原始的视口坐标，不加滚动偏移
              top: rect.top,
              left: rect.left,
              width: rect.width,
              height: rect.height,
            },
            iframePath: path, // 添加iframe路径信息
            cumulativeOffset: offset, // 添加累积偏移信息，这是相对于顶层窗口的偏移
            iframeScrollOffset: {
              // 添加当前iframe的滚动偏移信息
              top: scrollTop,
              left: scrollLeft,
            },
            // 添加当前iframe内部的滚动偏移（用于兼容）
            currentIframeScroll: currentScroll,
            // 标志：指示消息传递过程中是否需要累积每一层的滚动偏移
            needsCurrentLayerScroll: true,
          };

          // 向父级发送消息（会被逐层转发到顶层）
          if (window.parent !== window) {
            window.parent.postMessage(messageData, "*");
          }
        }, 10); // 微延迟确保 Range 已更新
      });

      document.addEventListener("pointerdown", (e) => {
        // 点击任何地方都通知上级
        const messageData = { type: "iframe-mousedown" };
        if (window.parent !== window) {
          window.parent.postMessage(messageData, "*");
        }
      });

      // 监听右键菜单事件，用于便签创建位置记录
      document.addEventListener("contextmenu", (e) => {
        // 获取当前iframe的路径和累积偏移
        const { path, offset, currentScroll } = getIframePathAndOffset();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

        const messageData = {
          type: "iframe-contextmenu",
          clientX: e.clientX, // 保持原始的视口坐标
          clientY: e.clientY, // 保持原始的视口坐标
          iframePath: path,
          cumulativeOffset: offset,
          iframeScrollOffset: {
            // 添加当前iframe的滚动偏移信息
            top: scrollTop,
            left: scrollLeft,
          },

          // 添加当前iframe内部的滚动偏移（用于兼容）
          currentIframeScroll: currentScroll,
          // 标志：指示消息传递过程中是否需要累积每一层的滚动偏移
          needsCurrentLayerScroll: true,
        };

        // 向父级发送消息（会被逐层转发到顶层）
        if (window.parent !== window) {
          window.parent.postMessage(messageData, "*");
        }
      });
    }
  },
});
