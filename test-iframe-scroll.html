<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层iframe嵌套滚动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .iframe-container {
            border: 2px solid #007bff;
            border-radius: 4px;
            overflow: hidden;
        }
        
        iframe {
            width: 100%;
            border: none;
        }
        
        .test-content {
            height: 2000px;
            padding: 20px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
        }
        
        .highlight-text {
            background-color: yellow;
            padding: 5px;
            margin: 10px 0;
            display: inline-block;
        }
        
        .scroll-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        滚动位置: <span id="scrollPos">0</span>px
    </div>
    
    <div class="container">
        <h1>多层iframe嵌套滚动测试</h1>
        <p>这个页面用于测试多层iframe嵌套中的划词菜单和便签位置计算。</p>
        
        <div class="test-section">
            <h2>测试说明</h2>
            <ul>
                <li>页面包含多层嵌套的iframe</li>
                <li>每层iframe都有滚动条</li>
                <li>在最深层iframe中进行划词操作</li>
                <li>右键点击创建便签</li>
                <li>验证划词菜单和便签位置是否正确</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>第一层iframe（高度400px）</h2>
            <div class="iframe-container">
                <iframe src="data:text/html;charset=utf-8,%3C!DOCTYPE%20html%3E%0A%3Chtml%3E%0A%3Chead%3E%0A%20%20%20%20%3Cmeta%20charset%3D%22UTF-8%22%3E%0A%20%20%20%20%3Ctitle%3E%E7%AC%AC%E4%B8%80%E5%B1%82iframe%3C/title%3E%0A%20%20%20%20%3Cstyle%3E%0A%20%20%20%20%20%20%20%20body%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20margin%3A%200%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20padding%3A%2020px%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20background%3A%20%23fff3e0%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20font-family%3A%20Arial%2C%20sans-serif%3B%0A%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%20%20.content%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20height%3A%201500px%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20background%3A%20linear-gradient(to%20bottom%2C%20%23fff3e0%2C%20%23ffcc02)%3B%0A%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%20%20.iframe-container%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20border%3A%202px%20solid%20%23ff9800%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20margin%3A%2020px%200%3B%0A%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%20%20iframe%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20width%3A%20100%25%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20border%3A%20none%3B%0A%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%3C/style%3E%0A%3C/head%3E%0A%3Cbody%3E%0A%20%20%20%20%3Ch2%3E%E7%AC%AC%E4%B8%80%E5%B1%82iframe%E5%86%85%E5%AE%B9%3C/h2%3E%0A%20%20%20%20%3Cp%3E%E8%BF%99%E6%98%AF%E7%AC%AC%E4%B8%80%E5%B1%82iframe%E7%9A%84%E5%86%85%E5%AE%B9%E3%80%82%E8%AF%B7%E5%90%91%E4%B8%8B%E6%BB%9A%E5%8A%A8%E6%9F%A5%E7%9C%8B%E7%AC%AC%E4%BA%8C%E5%B1%82iframe%E3%80%82%3C/p%3E%0A%20%20%20%20%3Cdiv%20class%3D%22content%22%3E%0A%20%20%20%20%20%20%20%20%3Cp%3E%E8%BF%99%E9%87%8C%E6%9C%89%E5%BE%88%E5%A4%9A%E5%86%85%E5%AE%B9%EF%BC%8C%E9%9C%80%E8%A6%81%E6%BB%9A%E5%8A%A8%E6%9F%A5%E7%9C%8B%E3%80%82%3C/p%3E%0A%20%20%20%20%20%20%20%20%3Cp%3E%E5%8F%AF%E4%BB%A5%E5%9C%A8%E8%BF%99%E9%87%8C%E8%BF%9B%E8%A1%8C%E5%88%92%E8%AF%8D%E6%B5%8B%E8%AF%95%E3%80%82%3C/p%3E%0A%20%20%20%20%20%20%20%20%3Cdiv%20class%3D%22iframe-container%22%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Ch3%3E%E7%AC%AC%E4%BA%8C%E5%B1%82iframe%EF%BC%88%E9%AB%98%E5%BA%A6300px%EF%BC%89%3C/h3%3E%0A%20%20%20%20%20%20%20%20%20%20%20%20%3Ciframe%20height%3D%22300%22%20src%3D%22data%3Atext/html%3Bcharset%3Dutf-8%2C%253C!DOCTYPE%2520html%253E%250A%253Chtml%253E%250A%253Chead%253E%250A%2520%2520%2520%2520%253Cmeta%2520charset%253D%2522UTF-8%2522%253E%250A%2520%2520%2520%2520%253Ctitle%253E%25E7%25AC%25AC%25E4%25BA%258C%25E5%25B1%2582iframe%253C/title%253E%250A%2520%2520%2520%2520%253Cstyle%253E%250A%2520%2520%2520%2520%2520%2520%2520%2520body%2520%257B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520margin%253A%25200%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520padding%253A%252020px%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520background%253A%2520%2523e8f5e8%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520font-family%253A%2520Arial%252C%2520sans-serif%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%257D%250A%2520%2520%2520%2520%2520%2520%2520%2520.content%2520%257B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520height%253A%25201000px%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520background%253A%2520linear-gradient(to%2520bottom%252C%2520%2523e8f5e8%252C%2520%252381c784)%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%257D%250A%2520%2520%2520%2520%2520%2520%2520%2520.highlight-text%2520%257B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520background-color%253A%2520%2523ffeb3b%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520padding%253A%25205px%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520margin%253A%252010px%25200%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520%2520display%253A%2520inline-block%253B%250A%2520%2520%2520%2520%2520%2520%2520%2520%257D%250A%2520%2520%2520%2520%253C/style%253E%250A%253C/head%253E%250A%253Cbody%253E%250A%2520%2520%2520%2520%253Ch2%253E%25E7%25AC%25AC%25E4%25BA%258C%25E5%25B1%2582iframe%25E5%2586%2585%25E5%25AE%25B9%253C/h2%253E%250A%2520%2520%2520%2520%253Cp%253E%25E8%25BF%2599%25E6%2598%25AF%25E7%25AC%25AC%25E4%25BA%258C%25E5%25B1%2582iframe%25E7%259A%2584%25E5%2586%2585%25E5%25AE%25B9%25E3%2580%2582%253C/p%253E%250A%2520%2520%2520%2520%253Cdiv%2520class%253D%2522content%2522%253E%250A%2520%2520%2520%2520%2520%2520%2520%2520%253Cp%253E%25E8%25BF%2599%25E9%2587%258C%25E6%259C%2589%25E5%25BE%2588%25E5%25A4%259A%25E5%2586%2585%25E5%25AE%25B9%25EF%25BC%258C%25E9%259C%2580%25E8%25A6%2581%25E6%25BB%259A%25E5%258A%25A8%25E6%259F%25A5%25E7%259C%258B%25E3%2580%2582%253C/p%253E%250A%2520%2520%2520%2520%2520%2520%2520%2520%253Cdiv%2520class%253D%2522highlight-text%2522%253E%25E8%25AF%25B7%25E5%259C%25A8%25E8%25BF%2599%25E9%2587%258C%25E8%25BF%259B%25E8%25A1%258C%25E5%2588%2592%25E8%25AF%258D%25E6%25B5%258B%25E8%25AF%2595%253C/div%253E%250A%2520%2520%2520%2520%2520%2520%2520%2520%253Cp%253E%25E6%25BB%259A%25E5%258A%25A8%25E5%2588%25B0%25E8%25BF%2599%25E9%2587%258C%25E5%2590%258E%25EF%25BC%258C%25E5%258F%25B3%25E9%2594%25AE%25E7%2582%25B9%25E5%2587%25BB%25E5%2588%259B%25E5%25BB%25BA%25E4%25BE%25BF%25E7%25AD%25BE%25E3%2580%2582%253C/p%253E%250A%2520%2520%2520%2520%2520%2520%2520%2520%253Cp%253E%25E6%25B5%258B%25E8%25AF%2595%25E5%2588%2592%25E8%25AF%258D%25E8%258F%259C%25E5%258D%2595%25E5%2592%258C%25E4%25BE%25BF%25E7%25AD%25BE%25E4%25BD%258D%25E7%25BD%25AE%25E6%2598%25AF%25E5%2590%25A6%25E6%25AD%25A3%25E7%25A1%25AE%25E3%2580%2582%253C/p%253E%250A%2520%2520%2520%2520%253C/div%253E%250A%253C/body%253E%250A%253C/html%253E%22%3E%3C/iframe%3E%0A%20%20%20%20%20%20%20%20%3C/div%3E%0A%20%20%20%20%3C/div%3E%0A%3C/body%3E%0A%3C/html%3E" height="400"></iframe>
            </div>
        </div>
        
        <div class="test-content">
            <h2>主页面内容</h2>
            <p>这是主页面的内容，用于测试页面滚动。</p>
            <div class="highlight-text">可以在这里进行划词测试</div>
            <p>向下滚动查看更多内容...</p>
            <p>测试划词菜单和便签位置是否正确。</p>
        </div>
    </div>
    
    <script>
        // 显示滚动位置
        function updateScrollIndicator() {
            const scrollPos = window.pageYOffset || document.documentElement.scrollTop;
            document.getElementById('scrollPos').textContent = Math.round(scrollPos);
        }
        
        window.addEventListener('scroll', updateScrollIndicator);
        updateScrollIndicator();
        
        // 添加一些调试信息
        console.log('测试页面已加载，可以开始测试iframe嵌套滚动场景');
    </script>
</body>
</html>
